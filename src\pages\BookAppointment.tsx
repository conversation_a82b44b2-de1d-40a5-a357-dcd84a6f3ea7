import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, MapPin, User } from 'lucide-react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import bg from '../../public/Images/bg4.png';
import Footer from '@/components/Footer';
import { useGetProfileImageByUserIdQuery } from '@/store/api/nurseApiSlice';

const timeUtils = {
  convertTimeToDisplayFormat: (timeString: string): string => {
    try {
      const time24h = timeString.match(/^\d{2}:\d{2}/)
        ? timeString.substring(0, 5)
        : new Date(timeString).toTimeString().substring(0, 5);
      const [hours, minutes] = time24h.split(':');
      const hour12 = Number.parseInt(hours) % 12 || 12;
      const ampm = Number.parseInt(hours) >= 12 ? 'PM' : 'AM';
      return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`;
    } catch {
      return timeString;
    }
  },

  getSortOrderFromTime: (displayTime: string): number => {
    const [time, ampm] = displayTime.split(' ');
    const [hours, minutes] = time.split(':');
    let hour24 = Number.parseInt(hours);
    if (ampm === 'AM' && hour24 === 12) hour24 = 0;
    if (ampm === 'PM' && hour24 !== 12) hour24 += 12;
    return hour24 * 60 + Number.parseInt(minutes);
  },

  convertTo24Hour: (time12h: string): string => {
    const [time, modifier] = time12h.split(' ');
    const [hours, minutes] = time.split(':');
    let hour24 = Number.parseInt(hours);
    if (modifier === 'AM' && hour24 === 12) hour24 = 0;
    else if (modifier === 'PM' && hour24 !== 12) hour24 += 12;
    return `${hour24.toString().padStart(2, '0')}:${minutes}`;
  },

  formatDateKey: (date: Date): string => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  getNext7Days: (): { day: string; date: string; fullDate: string }[] => {
    const today = new Date();
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const next7Days = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      next7Days.push({
        day: weekdays[date.getDay()],
        date: date.getDate().toString(),
        fullDate: timeUtils.formatDateKey(date),
      });
    }

    return next7Days;
  },

  isValidDate: (dateKey: string): boolean => {
    const today = new Date();
    const todayKey = timeUtils.formatDateKey(today);
    return dateKey >= todayKey;
  },
};

const NurseProfileImage = ({ nurseCognitoId }: { nurseCognitoId?: string }) => {
  const {
    data: profileImageData,
    isLoading: isProfileImageLoading,
    isError: isProfileImageError,
  } = useGetProfileImageByUserIdQuery(nurseCognitoId || '', {
    skip: !nurseCognitoId,
  });

  if (
    isProfileImageLoading ||
    isProfileImageError ||
    !profileImageData?.profile_image?.signed_url
  ) {
    return (
      <div className='h-16 w-16 bg-[#F2F2F2] text-nursery-blue rounded-full flex items-center justify-center'>
        <User className='h-10 w-10' />
      </div>
    );
  }

  return (
    <div className='h-20 w-20 p-1 bg-white rounded-full overflow-hidden flex items-center justify-center'>
      <img
        src={profileImageData.profile_image.signed_url}
        alt='Nurse profile'
        className='object-cover w-full h-full rounded-full'
        onError={e => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'flex';
          }
        }}
      />
      <div
        className='h-16 w-16 bg-[#F2F2F2] text-nursery-blue rounded-full flex items-center justify-center'
        style={{ display: 'none' }}
      >
        <User className='h-10 w-10' />
      </div>
    </div>
  );
};

const BookAppointment = () => {
  const [selectedDay, setSelectedDay] = useState('');
  const [daySlots, setDaySlots] = useState<Record<string, string[]>>({});
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [acceptedTerms, setAcceptedTerms] = useState(true);
  const [availableSlotsByDate, setAvailableSlotsByDate] = useState<
    Record<string, string[]>
  >({});
  const [days, setDays] = useState<
    {
      day: string;
      date: string;
      fullDate: string;
      hasSlots: boolean;
      disabled: boolean;
    }[]
  >([]);
  const [warningMessage, setWarningMessage] = useState('');
  const navigate = useNavigate();
  const [slotWarning, setSlotWarning] = useState('');

  const location = useLocation();
  const nurse = location.state?.nurse;
  const nurseSlots = location.state?.nurse?.available_slots;

  const nurseId = nurse?.nurse_id || nurse?.cognito_id || nurse?.id;

  nurseSlots?.forEach(slotObj => {
    const _slotDate = slotObj.date;
    const _slotTimes = slotObj.slots;
  });

  const transformBackendData = useCallback(() => {
    const slotsByDate: Record<string, string[]> = {};
    if (!nurseSlots || !Array.isArray(nurseSlots)) {
      return { slotsByDate };
    }

    nurseSlots.forEach((slotObj: { date: string; slots: string[] }) => {
      const dateKey = slotObj.date;
      if (timeUtils.isValidDate(dateKey)) {
        slotsByDate[dateKey] = slotObj.slots.map((time: string) =>
          timeUtils.convertTimeToDisplayFormat(time)
        );
        slotsByDate[dateKey].sort(
          (a, b) =>
            timeUtils.getSortOrderFromTime(a) -
            timeUtils.getSortOrderFromTime(b)
        );
      }
    });
    return { slotsByDate };
  }, [nurseSlots]);

  useEffect(() => {
    const { slotsByDate } = transformBackendData();
    setAvailableSlotsByDate(slotsByDate);

    const next7Days = timeUtils.getNext7Days();

    const daysWithAvailability = next7Days.map(day => ({
      ...day,
      hasSlots: Boolean(
        slotsByDate[day.fullDate] && slotsByDate[day.fullDate].length > 0
      ),
      disabled: !(
        slotsByDate[day.fullDate] && slotsByDate[day.fullDate].length > 0
      ),
    }));

    setDays(daysWithAvailability);

    const firstAvailableDay = daysWithAvailability.find(day => day.hasSlots);
    if (firstAvailableDay) {
      setSelectedDay(firstAvailableDay.fullDate);
    }
  }, [nurseSlots, transformBackendData]);

  const getAvailableSlotsForSelectedDate = () => {
    const slots = availableSlotsByDate[selectedDay] || [];

    const todayKey = timeUtils.formatDateKey(new Date());
    if (selectedDay === todayKey) {
      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      return slots.filter(slot => {
        const [hours, minutes] = timeUtils.convertTo24Hour(slot).split(':');
        const slotMinutes = parseInt(hours) * 60 + parseInt(minutes);
        return slotMinutes > currentMinutes;
      });
    }
    return slots;
  };

  const getCurrentDaySlots = () => {
    return daySlots[selectedDay] || [];
  };

  const isOnlyOneSlotSelected = () => {
    let count = 0;
    let selectedDate = '';
    Object.entries(daySlots).forEach(([date, slots]) => {
      if (slots.length > 0) {
        count += slots.length;
        selectedDate = date;
      }
    });
    return count === 1 && selectedDate === selectedDay;
  };

  useEffect(() => {
    let totalSlots = 0;
    let selectedDate = '';
    Object.entries(daySlots).forEach(([date, slots]) => {
      if (slots.length > 0) {
        totalSlots += slots.length;
        selectedDate = date;
      }
    });
    if (totalSlots > 1 || (totalSlots === 1 && selectedDate !== selectedDay)) {
      setSlotWarning('You can book only 1 slot at a time');
    } else {
      setSlotWarning('');
    }
  }, [daySlots, selectedDay]);

  const handleDaySelect = (fullDate: string, disabled: boolean) => {
    setWarningMessage('');

    if (disabled) {
      setWarningMessage('Slots not available for this date');
      return;
    }

    setSelectedDay(fullDate);
  };

  const handleSlotToggle = (slot: string) => {
    setDaySlots(prev => {
      const newDaySlots: Record<string, string[]> = {};
      Object.keys(prev).forEach(date => {
        newDaySlots[date] = [];
      });

      const currentSlots = prev[selectedDay] || [];
      let newSlots: string[];
      if (currentSlots.includes(slot)) {
        newSlots = [];
      } else {
        newSlots = [slot];
      }
      newDaySlots[selectedDay] = newSlots;
      return newDaySlots;
    });
  };

  const handleConfirm = () => {
    const _selectedDayData = days.find(day => day.fullDate === selectedDay);
    const currentSlots = getCurrentDaySlots();

    const slotsIn24Hour = currentSlots.map(slot =>
      timeUtils.convertTo24Hour(slot)
    );

    const bookingData = {
      nurse_cognitoId: nurseId,
      customer_cognitoId: nurse?.customerId,
      nurse_given_name: nurse?.name,
      customer_given_name: nurse?.customerName,
      nurse_location_latitude: nurse?.latitude?.toString() || '0',
      nurse_location_longitude: nurse?.longitude?.toString() || '0',
      nurse_location_address: nurse?.location || '',
      customer_booked_location_address: nurse?.customerAddress,
      customer_booked_location_latitude: nurse?.customerLatitude,
      customer_booked_location_longitude: nurse?.customerLongitude,
      services_selected: selectedServices,
      hourly_fare: nurse?.fees?.toString() || '0',
      booked_date: selectedDay,
      booked_slot: slotsIn24Hour.join(', '),
    };

    navigate('/location-confirmation', {
      state: {
        bookingDetails: {
          ...bookingData,
          selectedSlots: currentSlots,
        },
        nurse: nurse,
      },
    });
  };

  const availableSlots = getAvailableSlotsForSelectedDate();
  const currentSlots = getCurrentDaySlots();

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-4 sm:p-6'>
        <div className='absolute inset-0 w-full h-[220px] z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className=' object-cover w-full '
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        <div className=' relative flex items-center top-5 md:top-3 left-5 md:left-5 sm:mb-8'>
          <button onClick={() => navigate(-1)} className='mr-3 sm:mr-4'>
            <ArrowLeft className='h-6 w-6 sm:h-6 sm:w-6' />
          </button>
        </div>

        <div className='mt-4 flex items-center gap-3'>
          <div className='relative'>
            <NurseProfileImage nurseCognitoId={nurseId} />
          </div>
          <div className='relative flex-1'>
            <h1 className='text-lg sm:text-xl font-semibold'>
              {nurse?.name || 'Nurse'}
            </h1>
            <div className='flex items-center text-sm sm:text-base opacity-95 mt-1'>
              <MapPin className='md:w-5 md:h-5 w-5 h-5 items-center mr-1' />
              <span className='hidden md:inline'>
                {nurse?.location || 'Location not available'}
              </span>
              <span className='md:hidden'>
                {nurse?.location
                  ? nurse.location.split(' ').slice(0, 6).join(' ') +
                    (nurse.location.split(' ').length > 6 ? '...' : '')
                  : 'Location not available'}
              </span>
            </div>
            <div className='relative bg-nursery-blue text-white px-3 py-[5px] rounded-full text-xs sm:text-sm font-medium mt-2 inline-block'>
              ₹ {nurse?.fees || '0'}/ Hour
            </div>
          </div>
        </div>
      </header>

      <main className='flex-1 px-4 sm:px-6 pt-4 sm:pt-6 pb-20 sm:pb-8'>
        {}
        <section className='mb-6 sm:mb-8'>
          <h2 className='text-lg sm:text-xl font-semibold mb-4'>
            Schedule Slot
          </h2>

          {}
          {warningMessage && (
            <div className='mb-4 p-3  text-red-600 text-sm'>
              {warningMessage}
            </div>
          )}

          <div className='flex space-x-2 sm:space-x-3 overflow-x-auto mt-2 pb-2 scroll-smooth'>
            {days.map(day => (
              <DayButton
                key={day.fullDate}
                day={day.day}
                date={day.date}
                selected={selectedDay === day.fullDate && !day.disabled}
                hasSlots={day.hasSlots}
                disabled={day.disabled}
                onClick={() => handleDaySelect(day.fullDate, day.disabled)}
              />
            ))}
          </div>
        </section>

        {}
        <section className='mb-4 sm:mb-8'>
          <h2 className='text-lg sm:text-xl font-semibold mb-4'>
            Slots Available
          </h2>

          {}
          {!selectedDay && (
            <div className=' p-3 text-red-600 text-sm'>
              Slots not available for {nurse.name}
            </div>
          )}

          {}
          {slotWarning && (
            <div className='mb-2 p-2 text-red-600 text-sm'>{slotWarning}</div>
          )}

          <div className='grid grid-cols-3 sm:grid-cols-4 gap-2 sm:gap-3'>
            {availableSlots.map(slot => (
              <button
                key={slot}
                onClick={() => handleSlotToggle(slot)}
                className={`px-2 py-2 sm:px-2 sm:py-3 rounded-full text-sm sm:text-base font-medium transition-colors ${
                  currentSlots.includes(slot)
                    ? 'bg-nursery-blue text-white'
                    : 'bg-[#F2F2F2] text-gray-700 hover:bg-nursery-teal border border-gray-400'
                }`}
              >
                {slot}
              </button>
            ))}
          </div>
          {availableSlots.length === 0 && selectedDay && (
            <p className='text-gray-500 text-center py-4'>
              No slots available for selected date
            </p>
          )}
        </section>

        {}
        <section className='mb-6 sm:mb-8'>
          <h2 className='text-lg sm:text-xl font-semibold mb-4'>
            What type of nursing services?
          </h2>
          <div className='flex flex-wrap gap-2 sm:gap-3'>
            {nurse?.services?.map(service => (
              <button
                key={service}
                onClick={() => {
                  if (selectedServices.includes(service)) {
                    setSelectedServices(
                      selectedServices.filter(s => s !== service)
                    );
                  } else {
                    setSelectedServices([...selectedServices, service]);
                  }
                }}
                className={`px-3 py-2 sm:px-4 sm:py-2 rounded-full text-sm sm:text-base transition-colors ${
                  selectedServices.includes(service)
                    ? 'bg-nursery-blue text-white'
                    : 'bg-transparent text-gray-700 hover:bg-nursery-teal border border-gray-400'
                }`}
              >
                {service}
              </button>
            )) || <p className='text-gray-500'>No services available</p>}
          </div>
        </section>

        {}
        <section className='mb-6 sm:mb-8'>
          <div className='flex items-start space-x-3'>
            <input
              type='checkbox'
              id='terms'
              checked={acceptedTerms}
              onChange={e => setAcceptedTerms(e.target.checked)}
              className='mt-1 w-4 h-4 sm:w-5 sm:h-5 text-nursery-blue bg-white border-gray-300 rounded focus:ring-nursery-blue focus:ring-2 accent-nursery-blue'
            />
            <label
              htmlFor='terms'
              className='text-sm sm:text-base text-gray-700'
            >
              I have read{' '}
              <Link
                to='/terms-conditios'
                target='_blank'
                className='text-nursery-blue font-medium hover:underline'
              >
                Terms & Conditions
              </Link>{' '}
              and agree
            </label>
          </div>
        </section>

        {}
        <div className='mb-3 text-center'>
          <button
            onClick={handleConfirm}
            disabled={
              !acceptedTerms ||
              !isOnlyOneSlotSelected() ||
              selectedServices.length === 0 ||
              !selectedDay ||
              slotWarning !== ''
            }
            className='w-full md:w-1/4 mx-auto bg-nursery-darkBlue hover:bg-nursery-blue text-white py-2 font-semibold rounded-lg disabled:bg-gray-400 disabled:cursor-not-allowed shadow-xl'
          >
            Confirm and Next
          </button>
        </div>
      </main>

      {}
      <Footer />
    </div>
  );
};

interface DayButtonProps {
  day: string;
  date: string;
  selected: boolean;
  hasSlots: boolean;
  disabled: boolean;
  onClick: () => void;
}

const DayButton = ({
  day,
  date,
  selected,
  hasSlots,
  disabled,
  onClick,
}: DayButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={`relative flex flex-col items-center justify-center rounded-full w-14 h-16 sm:w-16 sm:h-20 transition-all duration-200 flex-shrink-0 ${
        disabled
          ? 'bg-gray-300 text-gray-500 opacity-50'
          : selected
            ? 'bg-nursery-blue text-white'
            : 'bg-[#F2F2F2] text-gray-700 hover:bg-nursery-teal border-2 border-gray-300 hover:border-none'
      }`}
    >
      {hasSlots && !disabled && (
        <div className='absolute -top-1 -right-1 w-3 h-3 bg-green-500 mt-1 rounded-full border-2 border-white'></div>
      )}
      <span className='text-xs sm:text-sm font-medium'>{day}</span>
      <span className='text-lg sm:text-xl font-semibold mt-1'>{date}</span>
    </button>
  );
};

export default BookAppointment;
